
import {Link, useLocation} from "react-router-dom"

import {cn} from "@/lib/utils"
import {useAuth} from "@/context/AuthContext"
import {LanguageSelector} from "@/components/LanguageSelector.tsx";

function Navbar() {
    const {isAuthenticated, user} = useAuth()
    const {pathname} = useLocation()

    return (
        <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="container flex h-14 items-center">
                <Link to="/" className="mr-6 flex items-center space-x-2">
          <span className="hidden font-bold sm:inline-block">
            MySocialPoster
          </span>
                </Link>
                <nav className="flex items-center gap-6 text-sm">
                    <Link
                        to="/about"
                        className={cn(
                            "transition-colors hover:text-foreground/80",
                            pathname === "/about" ? "text-foreground" : "text-foreground/60"
                        )}
                    >
                        About
                    </Link>
                    <Link
                        to="/ai-agents"
                        className={cn(
                            "transition-colors hover:text-foreground/80",
                            pathname === "/ai-agents" ? "text-foreground" : "text-foreground/60"
                        )}
                    >
                        AI Agents
                    </Link>
                    {/*<Link*/}
                    {/*  to="/dashboard"*/}
                    {/*  className={cn(*/}
                    {/*    "transition-colors hover:text-foreground/80",*/}
                    {/*    pathname === "/dashboard" ? "text-foreground" : "text-foreground/60"*/}
                    {/*  )}*/}
                    {/*>*/}
                    {/*  Dashboard*/}
                    {/*</Link>*/}
                    {/*<Link*/}
                    {/*  to="/thought-leader"*/}
                    {/*  className={cn(*/}
                    {/*    "transition-colors hover:text-foreground/80",*/}
                    {/*    pathname === "/thought-leader" ? "text-foreground" : "text-foreground/60"*/}
                    {/*  )}*/}
                    {/*>*/}
                    {/*  Thought Leader*/}
                    {/*</Link>*/}
                    {/*<Link*/}
                    {/*  to="/pricing"*/}
                    {/*  className={cn(*/}
                    {/*    "transition-colors hover:text-foreground/80",*/}
                    {/*    pathname === "/pricing" ? "text-foreground" : "text-foreground/60"*/}
                    {/*  )}*/}
                    {/*>*/}
                    {/*  Pricing*/}
                    {/*</Link>*/}
                </nav>

                <div className="ml-auto flex items-center space-x-2">
                    {isAuthenticated ? (
                        <>
                            <span>{user?.email}</span>
                            <Link
                                to="/settings"
                                className={cn(
                                    "transition-colors hover:text-foreground/80",
                                    pathname === "/settings" ? "text-foreground" : "text-foreground/60"
                                )}
                            >
                                Settings
                            </Link>
                        </>
                    ) : (
                        <>
                            <LanguageSelector/>
                            <Link
                                to="/login"
                                className={cn(
                                    "transition-colors hover:text-foreground/80",
                                    pathname === "/login" ? "text-foreground" : "text-foreground/60"
                                )}
                            >
                                Login
                            </Link>
                            <Link
                                to="/signup"
                                className={cn(
                                    "transition-colors hover:text-foreground/80",
                                    pathname === "/signup" ? "text-foreground" : "text-foreground/60"
                                )}
                            >
                                Sign Up
                            </Link>
                        </>
                    )}
                </div>
            </div>
        </header>
    )
}

export default Navbar;
