
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { Loader2, <PERSON>ed<PERSON>, Facebook, Instagram } from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { SocialPlatform } from "@/context/PostPreferencesContext";
import { useLinkedInOAuthFlow } from "@/hooks/useLinkedInOAuthFlow";

type ConnectorProps = {
  platform: SocialPlatform;
  title: string;
  description: string;
  icon: React.ReactNode;
  isConnected: boolean;
  onConnect: () => Promise<void>;
  onDisconnect: () => Promise<void>;
};

const SocialConnector = ({ 
  platform, 
  title, 
  description, 
  icon, 
  isConnected, 
  onConnect, 
  onDisconnect 
}: ConnectorProps) => {
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const handleAction = async () => {
    setLoading(true);
    try {
      if (isConnected) {
        await onDisconnect();
        toast({
          title: "Disconnected",
          description: `Your ${title} account has been disconnected.`,
        });
      } else {
        await onConnect();
      }
    } catch (error) {
      console.error(`${platform} connection error:`, error);
      toast({
        title: "Connection Error",
        description: `There was a problem connecting to ${title}. Please try again.`,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-xl">{title}</CardTitle>
          {icon}
        </div>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <Button
          onClick={handleAction}
          disabled={loading}
          variant={isConnected ? "outline" : "default"}
          className="w-full"
        >onDisconnect
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Processing...
            </>
          ) : isConnected ? (
            "Disconnect"
          ) : (
            "Connect"
          )}
        </Button>
      </CardContent>
    </Card>
  );
};

export function SocialConnectors() {
  const { user } = useAuth();
  const { startLinkedInAuth } = useLinkedInOAuthFlow();

  // LinkedIn connection handler using OAuth flow
  const connectLinkedIn = async () => {
    startLinkedInAuth();
  };

  // Mock functions for other platforms
  const connectFacebook = async () => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    // In a real implementation, this would handle OAuth flow for Facebook
  };

  const connectInstagram = async () => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    // In a real implementation, this would handle OAuth flow for Instagram
  };

  // Mock functions for disconnecting platforms
  const disconnectLinkedIn = async () => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    // In a real implementation, this would revoke OAuth tokens
  };

  const disconnectFacebook = async () => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
  };

  const disconnectInstagram = async () => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
  };

  return (
    <div className="space-y-8">
      <h2 className="text-2xl font-bold">Social Media Connectors</h2>
      <p className="text-gray-600">
        Connect your social media accounts to enable automatic posting. MySocialPoster will only post content you approve.
      </p>

      <div className="grid md:grid-cols-3 gap-6">
        <SocialConnector
          platform="linkedin"
          title="LinkedIn"
          description="Post articles with professional descriptions and hashtags to your LinkedIn profile and managed organization pages."
          icon={<Linkedin className="h-6 w-6 text-linkedin-blue" />}
          isConnected={user?.linkedin_connected || false}
          onConnect={connectLinkedIn}
          onDisconnect={disconnectLinkedIn}
        />

        <SocialConnector
          platform="facebook"
          title="Facebook"
          description="Share content to your Facebook profile or business pages."
          icon={<Facebook className="h-6 w-6 text-facebook-blue" />}
          isConnected={false}
          onConnect={connectFacebook}
          onDisconnect={disconnectFacebook}
        />

        <SocialConnector
          platform="instagram"
          title="Instagram"
          description="Post to your Instagram business account and pages."
          icon={<Instagram className="h-6 w-6 text-instagram-pink" />}
          isConnected={false}
          onConnect={connectInstagram}
          onDisconnect={disconnectInstagram}
        />
      </div>
    </div>
  );
}
